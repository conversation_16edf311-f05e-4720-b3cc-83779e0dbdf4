<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Q&A Video Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1em;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-label {
            color: white;
            font-size: 1.1em;
            margin-bottom: 10px;
            display: block;
        }

        .json-input {
            width: 100%;
            height: 150px;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 15px;
            color: white;
            font-size: 14px;
            resize: vertical;
            backdrop-filter: blur(5px);
            transition: border-color 0.3s ease;
        }

        .json-input:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
        }

        .json-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .btn.secondary:hover {
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .display-area {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 30px;
            min-height: 400px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .qa-item {
            margin-bottom: 40px;
            opacity: 0;
            transform: translateY(30px);
            animation: slideIn 0.6s ease forwards;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }

        .qa-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
            border-radius: 15px 15px 0 0;
        }

        .qa-item:nth-child(even) {
            animation-delay: 0.2s;
        }

        .qa-item:nth-child(odd) {
            animation-delay: 0.4s;
        }

        .question {
            color: #ffffff;
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .question::before {
            content: "Q: ";
            color: #ff6b6b;
            font-weight: 700;
        }

        .answer {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1em;
            line-height: 1.6;
            padding-left: 20px;
            border-left: 3px solid #4ecdc4;
            margin-top: 10px;
        }

        .answer::before {
            content: "A: ";
            color: #4ecdc4;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 1.2em;
            padding: 60px 20px;
        }

        .empty-state .icon {
            font-size: 3em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .recording-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 0, 0, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            display: none;
            animation: pulse 2s infinite;
        }

        .recording-indicator.active {
            display: block;
        }

        @keyframes slideIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .floating-shapes {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 20s infinite linear;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: #ff6b6b;
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 70%;
            left: 80%;
            width: 60px;
            height: 60px;
            background: #4ecdc4;
            border-radius: 50%;
            animation-delay: 5s;
        }

        .shape:nth-child(3) {
            top: 50%;
            left: 5%;
            width: 100px;
            height: 100px;
            background: #45b7d1;
            border-radius: 50%;
            animation-delay: 10s;
        }

        @keyframes float {
            0% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
            100% { transform: translateY(0px) rotate(360deg); }
        }

        .error-message {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 107, 107, 0.3);
            display: none;
        }

        .success-message {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid rgba(78, 205, 196, 0.3);
            display: none;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .controls {
                justify-content: center;
            }
            
            .btn {
                flex: 1;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="recording-indicator" id="recordingIndicator">
        ● Recording...
    </div>

    <div class="container">
        <div class="header">
            <h1>Q&A Video Generator</h1>
            <p>Transform your questions and answers into beautiful visual presentations</p>
        </div>

        <div class="input-section">
            <label class="input-label">Enter your JSON Q&A data:</label>
            <textarea 
                class="json-input" 
                id="jsonInput" 
                placeholder='[
  {
    "question": "What is artificial intelligence?",
    "answer": "Artificial intelligence is the simulation of human intelligence in machines that are programmed to think and learn like humans."
  },
  {
    "question": "How does machine learning work?",
    "answer": "Machine learning works by using algorithms to analyze data, identify patterns, and make predictions or decisions without being explicitly programmed for each task."
  }
]'></textarea>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <div class="controls">
            <button class="btn" onclick="displayQuestions()">Display Q&A</button>
            <button class="btn secondary" onclick="startRecording()" id="recordBtn">Start Recording</button>
            <button class="btn secondary" onclick="stopRecording()" id="stopBtn" disabled>Stop Recording</button>
            <button class="btn" onclick="clearDisplay()">Clear</button>
        </div>

        <div class="display-area" id="displayArea">
            <div class="empty-state">
                <div class="icon">📝</div>
                <div>Enter your JSON Q&A data above and click "Display Q&A" to get started</div>
            </div>
        </div>
    </div>

    <script>
        let mediaRecorder;
        let recordedChunks = [];
        let isRecording = false;

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }

        function displayQuestions() {
            const jsonInput = document.getElementById('jsonInput').value.trim();
            const displayArea = document.getElementById('displayArea');

            if (!jsonInput) {
                showError('Please enter JSON data first');
                return;
            }

            try {
                const qaData = JSON.parse(jsonInput);
                
                if (!Array.isArray(qaData)) {
                    throw new Error('JSON must be an array of question-answer objects');
                }

                displayArea.innerHTML = '';
                
                qaData.forEach((item, index) => {
                    if (!item.question || !item.answer) {
                        throw new Error(`Item ${index + 1} must have both "question" and "answer" properties`);
                    }

                    const qaItem = document.createElement('div');
                    qaItem.className = 'qa-item';
                    qaItem.style.animationDelay = `${index * 0.2}s`;
                    
                    qaItem.innerHTML = `
                        <div class="question">${escapeHtml(item.question)}</div>
                        <div class="answer">${escapeHtml(item.answer)}</div>
                    `;
                    
                    displayArea.appendChild(qaItem);
                });

                showSuccess(`Successfully displayed ${qaData.length} Q&A pairs`);
                
            } catch (error) {
                showError('Invalid JSON format: ' + error.message);
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function clearDisplay() {
            const displayArea = document.getElementById('displayArea');
            displayArea.innerHTML = `
                <div class="empty-state">
                    <div class="icon">📝</div>
                    <div>Enter your JSON Q&A data above and click "Display Q&A" to get started</div>
                </div>
            `;
        }

        async function startRecording() {
            try {
                const displayArea = document.getElementById('displayArea');
                
                if (displayArea.innerHTML.includes('empty-state')) {
                    showError('Please display some Q&A content first');
                    return;
                }

                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'screen'
                    },
                    audio: false
                });

                mediaRecorder = new MediaRecorder(stream);
                recordedChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };

                mediaRecorder.onstop = () => {
                    const blob = new Blob(recordedChunks, { type: 'video/webm' });
                    const url = URL.createObjectURL(blob);
                    
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `qa-video-${new Date().toISOString().slice(0, 10)}.webm`;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                    showSuccess('Video saved successfully!');
                };

                mediaRecorder.start();
                isRecording = true;
                
                document.getElementById('recordBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('recordingIndicator').classList.add('active');
                
                showSuccess('Recording started! Select the browser tab to record.');
                
            } catch (error) {
                showError('Error starting recording: ' + error.message);
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                
                document.getElementById('recordBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                document.getElementById('recordingIndicator').classList.remove('active');
            }
        }

        // Auto-animate existing content when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const qaItems = document.querySelectorAll('.qa-item');
            qaItems.forEach((item, index) => {
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });

        // Handle keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                displayQuestions();
            }
        });
    </script>
</body>
</html>